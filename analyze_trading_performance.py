#!/usr/bin/env python3
"""
Comprehensive Trading Performance Analysis
Analyze root causes of consecutive losses and identify improvement opportunities
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import json

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

import MetaTrader5 as mt5
from account_management.account_manager import <PERSON><PERSON><PERSON><PERSON>ana<PERSON>

def analyze_account_performance(account):
    """Analyze performance for a specific account"""
    print(f"\n🔍 ANALYZING ACCOUNT: {account.account_id} ({account.account_number})")
    print("=" * 60)
    
    try:
        # Login to account
        login_result = mt5.login(
            login=account.account_number,
            password=account.password,
            server=account.server
        )
        
        if not login_result:
            print(f"❌ Failed to login: {mt5.last_error()}")
            return None
        
        # Get account info
        account_info = mt5.account_info()
        if not account_info:
            print("❌ Failed to get account info")
            return None
        
        print(f"💰 Current Balance: ${account_info.balance:.2f}")
        print(f"💰 Current Equity: ${account_info.equity:.2f}")
        print(f"📊 Current Profit: ${account_info.profit:.2f}")
        print(f"📊 Margin Level: {account_info.margin_level:.1f}%")
        
        # Analyze trade history (last 7 days)
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)
        
        deals = mt5.history_deals_get(start_time, end_time)
        if not deals:
            print("📜 No recent deals found")
            return {
                'account_id': account.account_id,
                'balance': account_info.balance,
                'equity': account_info.equity,
                'profit': account_info.profit,
                'total_deals': 0,
                'winning_deals': 0,
                'losing_deals': 0,
                'total_profit': 0,
                'total_loss': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'issues': []
            }
        
        # Analyze deals
        total_deals = len([d for d in deals if d.profit != 0])
        winning_deals = len([d for d in deals if d.profit > 0])
        losing_deals = len([d for d in deals if d.profit < 0])
        total_profit = sum(d.profit for d in deals if d.profit > 0)
        total_loss = abs(sum(d.profit for d in deals if d.profit < 0))
        
        win_rate = (winning_deals / total_deals * 100) if total_deals > 0 else 0
        profit_factor = (total_profit / total_loss) if total_loss > 0 else 0
        
        print(f"\n📈 PERFORMANCE METRICS (7 days):")
        print(f"   Total Deals: {total_deals}")
        print(f"   Winning Deals: {winning_deals}")
        print(f"   Losing Deals: {losing_deals}")
        print(f"   Win Rate: {win_rate:.1f}%")
        print(f"   Total Profit: ${total_profit:.2f}")
        print(f"   Total Loss: ${total_loss:.2f}")
        print(f"   Net P&L: ${total_profit - total_loss:.2f}")
        print(f"   Profit Factor: {profit_factor:.2f}")
        
        # Identify issues
        issues = []
        
        if win_rate < 40:
            issues.append(f"LOW_WIN_RATE: {win_rate:.1f}% (target: >40%)")
        
        if profit_factor < 1.2:
            issues.append(f"LOW_PROFIT_FACTOR: {profit_factor:.2f} (target: >1.2)")
        
        if total_deals > 20:  # Too many trades
            issues.append(f"OVERTRADING: {total_deals} deals in 7 days (target: <15)")
        
        if account_info.margin_level < 300:
            issues.append(f"HIGH_RISK: Margin level {account_info.margin_level:.1f}% (target: >300%)")
        
        # Check for consecutive losses
        consecutive_losses = 0
        max_consecutive_losses = 0
        for deal in sorted(deals, key=lambda x: x.time):
            if deal.profit < 0:
                consecutive_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
            else:
                consecutive_losses = 0
        
        if max_consecutive_losses > 3:
            issues.append(f"CONSECUTIVE_LOSSES: {max_consecutive_losses} in a row (target: <4)")
        
        # Analyze recent large losses
        large_losses = [d for d in deals if d.profit < -2.0]
        if large_losses:
            issues.append(f"LARGE_LOSSES: {len(large_losses)} losses > $2 (total: ${sum(d.profit for d in large_losses):.2f})")
            print(f"\n🔴 LARGE LOSSES DETECTED:")
            for loss in large_losses[-5:]:  # Show last 5
                loss_time = datetime.fromtimestamp(loss.time)
                print(f"   {loss_time.strftime('%m/%d %H:%M')} - {loss.symbol} ${loss.profit:.2f}")
        
        if issues:
            print(f"\n⚠️  ISSUES IDENTIFIED:")
            for issue in issues:
                print(f"   • {issue}")
        else:
            print(f"\n✅ No major issues identified")
        
        return {
            'account_id': account.account_id,
            'balance': account_info.balance,
            'equity': account_info.equity,
            'profit': account_info.profit,
            'total_deals': total_deals,
            'winning_deals': winning_deals,
            'losing_deals': losing_deals,
            'total_profit': total_profit,
            'total_loss': total_loss,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'max_consecutive_losses': max_consecutive_losses,
            'issues': issues
        }
        
    except Exception as e:
        print(f"❌ Error analyzing account: {e}")
        return None

def main():
    print("🔍 COMPREHENSIVE TRADING PERFORMANCE ANALYSIS")
    print("=" * 60)
    
    # Initialize MT5
    if not mt5.initialize():
        print('❌ Failed to initialize MT5')
        return False
    
    try:
        # Load accounts
        account_manager = AccountManager()
        accounts = account_manager.get_all_accounts()
        
        print(f"📊 Analyzing {len(accounts)} accounts...")
        
        all_results = []
        total_balance = 0
        total_profit = 0
        total_issues = 0
        
        for account in accounts:
            result = analyze_account_performance(account)
            if result:
                all_results.append(result)
                total_balance += result['balance']
                total_profit += result['profit']
                total_issues += len(result['issues'])
        
        # Overall summary
        print(f"\n🎯 OVERALL SYSTEM PERFORMANCE")
        print("=" * 60)
        print(f"Total Accounts: {len(all_results)}")
        print(f"Total Balance: ${total_balance:.2f}")
        print(f"Total Unrealized P&L: ${total_profit:.2f}")
        print(f"Total Issues Identified: {total_issues}")
        
        # Identify system-wide issues
        print(f"\n🔧 RECOMMENDED FIXES:")
        
        low_win_rate_accounts = [r for r in all_results if any('LOW_WIN_RATE' in issue for issue in r['issues'])]
        if low_win_rate_accounts:
            print(f"   • {len(low_win_rate_accounts)} accounts have low win rates - Review signal quality and entry criteria")
        
        overtrading_accounts = [r for r in all_results if any('OVERTRADING' in issue for issue in r['issues'])]
        if overtrading_accounts:
            print(f"   • {len(overtrading_accounts)} accounts are overtrading - Increase signal intervals and add trade limits")
        
        high_risk_accounts = [r for r in all_results if any('HIGH_RISK' in issue for issue in r['issues'])]
        if high_risk_accounts:
            print(f"   • {len(high_risk_accounts)} accounts have high risk - Reduce position sizes and improve risk management")
        
        consecutive_loss_accounts = [r for r in all_results if any('CONSECUTIVE_LOSSES' in issue for issue in r['issues'])]
        if consecutive_loss_accounts:
            print(f"   • {len(consecutive_loss_accounts)} accounts have consecutive losses - Implement loss streak protection")
        
        # Save results
        with open('performance_analysis_results.json', 'w') as f:
            json.dump({
                'analysis_time': datetime.now().isoformat(),
                'accounts': all_results,
                'summary': {
                    'total_accounts': len(all_results),
                    'total_balance': total_balance,
                    'total_profit': total_profit,
                    'total_issues': total_issues
                }
            }, f, indent=2)
        
        print(f"\n📄 Results saved to performance_analysis_results.json")
        return True
        
    finally:
        mt5.shutdown()

if __name__ == "__main__":
    main()
