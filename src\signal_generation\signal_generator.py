"""
AI-Driven Signal Generation System
"""

import asyncio
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
import schedule
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from ai_integration.qwen_client import QwenClient
from ai_integration.prompt_builder import Prompt<PERSON>uilder
from mt5_integration.mt5_client import MT5<PERSON><PERSON>
from account_management.account_manager import AccountManager
from strategies.base_strategy import MarketData
from money_management.base_strategy import AccountInfo
from logging_system.logger import get_logger, trading_logger
from validation.trade_validator import TradeValidator, ValidationResult
from mt5_integration.session_manager import session_manager
from utils.symbol_mapper import get_symbol_mapper
from strategy_selection.dynamic_strategy_selector import dynamic_strategy_selector
from risk_management.loss_prevention import loss_prevention_system

logger = get_logger(__name__)

class SignalGenerator:
    """Generates trading signals using AI for multiple accounts"""
    
    def __init__(self, account_manager: AccountManager):
        self.account_manager = account_manager
        self.prompt_builder = PromptBuilder()
        # Initialize MT5Client with AI client for error handling
        self.ai_client = None  # Will be set when <PERSON>wenClient is available
        self.mt5_client = MT5Client()  # Will be updated with AI client later
        self.trade_validator = TradeValidator()
        self.symbol_mapper = get_symbol_mapper()
        self.running = False
        self.last_signal_time = {}  # Track last signal time per symbol
        self.min_signal_interval = int(os.getenv('MIN_SIGNAL_INTERVAL_MINUTES', '15'))  # Prevent overtrading

        # Load default risk management settings from environment (fallbacks)
        self.default_max_daily_trades = int(os.getenv('MAX_DAILY_TRADES_PER_ACCOUNT', '5'))
        self.default_max_open_positions = int(os.getenv('MAX_OPEN_POSITIONS', '3'))
        self.default_max_pending_orders = int(os.getenv('MAX_PENDING_ORDERS', '5'))
        self.default_max_daily_loss = float(os.getenv('MAX_DAILY_LOSS_DOLLARS', '5.0'))
        self.default_max_drawdown_percent = float(os.getenv('MAX_DRAWDOWN_PERCENT', '10.0'))

        # Track daily statistics
        self.daily_trades = {}  # account_id -> trade_count
        self.daily_pnl = {}     # account_id -> daily_pnl

        # Market hours configuration
        self.market_hours = {
            'forex': {
                'start_hour': 0,  # Sunday 22:00 GMT (Monday 00:00)
                'end_hour': 22,   # Friday 22:00 GMT
                'weekend_start': 5,  # Friday
                'weekend_end': 0     # Sunday
            }
        }
    
    async def start(self):
        """Start the signal generation system"""
        try:
            logger.info("Starting signal generation system...")
            
            # Initialize MT5
            if not self.mt5_client.initialize():
                logger.error("Failed to initialize MT5")
                return False
            
            self.running = True
            
            # Schedule signal generation
            schedule.every(self.min_signal_interval).minutes.do(self._schedule_signal_generation)
            
            # Start scheduler loop
            while self.running:
                schedule.run_pending()
                await asyncio.sleep(60)  # Check every minute
            
            logger.info("Signal generation system stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error starting signal generator: {e}")
            return False
    
    def stop(self):
        """Stop the signal generation system"""
        self.running = False
        self.mt5_client.shutdown()
        logger.info("Signal generation system stop requested")
    
    def _schedule_signal_generation(self):
        """Schedule signal generation (called by scheduler)"""
        try:
            # Run async signal generation in event loop
            asyncio.create_task(self.generate_signals())
        except Exception as e:
            logger.error(f"Error scheduling signal generation: {e}")
    
    async def generate_signals(self):
        """Generate trading signals for all account groups"""
        try:
            if not self._is_market_open():
                logger.info("Market is closed, skipping signal generation")
                return
            
            logger.info("Starting signal generation cycle...")
            
            # Get account groups for optimization
            account_groups = self.account_manager.get_account_groups()

            # If no groups exist, create virtual groups from individual accounts
            if not account_groups:
                logger.info("No account groups found, creating virtual groups from individual accounts")
                account_groups = self._create_virtual_groups()

            async with QwenClient() as qwen_client:
                # Update MT5Client with AI client for error handling
                if not self.ai_client:
                    self.ai_client = qwen_client
                    self.mt5_client = MT5Client(ai_client=qwen_client)
                    logger.info("🤖 MT5Client updated with AI error handling capabilities")

                for group in account_groups:
                    await self._process_account_group(group, qwen_client)
            
            logger.info("Signal generation cycle completed")
            
        except Exception as e:
            logger.error(f"Error in signal generation: {e}")

    async def _process_all_accounts(self):
        """Process all accounts for signal generation (used by tests)"""
        await self.generate_signals()

    def _create_virtual_groups(self) -> List[Dict[str, Any]]:
        """Create virtual groups from individual accounts by grouping by strategy/money management"""
        groups = {}

        for account in self.account_manager.get_all_accounts():
            key = f"{account.strategy_type}_{account.money_management_type}"

            if key not in groups:
                groups[key] = {
                    'group_id': key,
                    'strategy': account.strategy_type,
                    'money_management': account.money_management_type,
                    'accounts': []
                }

            # Convert account to dict format expected by the processor
            account_dict = {
                'account_id': account.account_id,
                'account_number': account.account_number,
                'server': account.server,
                'password': account.password,
                'symbols': account.symbols,  # Already in correct format: [{"symbol": "EURUSD", "timeframe": "M15"}]
                'money_management_settings': account.money_management_config  # CRITICAL FIX: Use correct key name
            }
            groups[key]['accounts'].append(account_dict)

        logger.info(f"Created {len(groups)} virtual groups from individual accounts")
        return list(groups.values())

    async def _process_account_group(self, group: Dict[str, Any], qwen_client: QwenClient):
        """Process a group of accounts with same strategy/money management"""
        try:
            strategy_name = group['strategy']
            money_management_name = group['money_management']
            accounts = group['accounts']

            logger.info(f"Processing group: {strategy_name}/{money_management_name} with {len(accounts)} accounts")

            # IMPORTANT: Process each account individually to preserve account-specific settings
            # Even though they're grouped for efficiency, each account needs its own configuration
            for account in accounts:
                await self._process_individual_account_in_group(
                    account, strategy_name, money_management_name, qwen_client
                )

        except Exception as e:
            logger.error(f"Error processing account group: {e}")

    async def _process_individual_account_in_group(
        self,
        account: Dict[str, Any],
        strategy_name: str,
        money_management_name: str,
        qwen_client: QwenClient
    ):
        """Process individual account with its specific settings"""
        try:
            # Get strategy and money management instances with account-specific config
            from strategies.base_strategy import StrategyType
            from money_management.base_strategy import MoneyManagementType

            # Convert string names to enum types
            strategy_type = StrategyType(strategy_name)
            mm_type = MoneyManagementType(money_management_name)

            # Create instances with account-specific config
            account_mm_settings = account.get('money_management_settings', {})

            # DYNAMIC STRATEGY SELECTION: Check if dynamic strategy selection is enabled
            strategy_config = account.get('strategy_selection', {})
            if strategy_config.get('mode') == 'dynamic':
                # Get preliminary market data for strategy selection
                try:
                    # Login to account to get market data for strategy selection
                    from account_management.models import TradingAccount
                    account_obj = TradingAccount(
                        account_id=account['account_id'],
                        account_number=account['account_number'],
                        server=account['server'],
                        username=account.get('username', ''),
                        password=account['password'],
                        strategy_type=account.get('strategy_type', ''),
                        money_management_type=account.get('money_management_type', ''),
                        symbols=account.get('symbols', []),
                        timeframes=account.get('timeframes', []),
                        money_management_config=account.get('money_management_config', {})
                    )

                    if self.mt5_client.login(account_obj):
                        # Get account info for strategy selection
                        account_balance = self.mt5_client.get_account_info()
                        if account_balance:
                            account_info = AccountInfo(
                                balance=account_balance.balance,
                                equity=account_balance.equity,
                                margin=account_balance.margin,
                                free_margin=account_balance.free_margin,
                                margin_level=account_balance.margin_level,
                                currency=account_balance.currency,
                                leverage=account_balance.leverage
                            )

                            # Get market data for first symbol to determine strategy
                            first_symbol = account.get('symbols', [{}])[0]
                            if first_symbol:
                                symbol_name = first_symbol.get('symbol', 'EURUSD')
                                timeframe = first_symbol.get('timeframe', 'M15')

                                market_data_dict = self.mt5_client.get_market_data(symbol_name, timeframe, 50)
                                if market_data_dict:
                                    market_data = MarketData(
                                        symbol=symbol_name,
                                        timeframe=market_data_dict['timeframe'],
                                        candles=market_data_dict['candles'],
                                        current_price=market_data_dict['current_price'],
                                        spread=market_data_dict['spread'],
                                        volume=market_data_dict['volume'],
                                        volatility=market_data_dict['volatility'],
                                        pip_size=market_data_dict.get('pip_size'),
                                        pip_value=market_data_dict.get('pip_value'),
                                        min_volume=market_data_dict.get('min_volume'),
                                        max_volume=market_data_dict.get('max_volume')
                                    )

                                    # Select optimal strategy dynamically
                                    selected_strategy_name = dynamic_strategy_selector.select_strategy(
                                        account, market_data, account_info
                                    )

                                    # Update strategy type if different from default
                                    if selected_strategy_name != strategy_name:
                                        logger.info(f"🎯 DYNAMIC STRATEGY: Account {account['account_id']} - "
                                                   f"Changed from {strategy_name} to {selected_strategy_name}")
                                        strategy_name = selected_strategy_name
                                        strategy_type = StrategyType(selected_strategy_name)

                                        # Log strategy change
                                        trading_logger.log_system_event(
                                            "DYNAMIC_STRATEGY_SELECTION",
                                            f"Account {account['account_id']}: Strategy changed to {selected_strategy_name} "
                                            f"based on market conditions (volatility: {market_data.volatility:.4f})",
                                            "INFO"
                                        )
                except Exception as e:
                    logger.error(f"Error in dynamic strategy selection for {account['account_id']}: {e}")
                    # Continue with original strategy

            strategy = self.account_manager.strategy_factory.create_strategy(strategy_type, {})
            money_management = self.account_manager.money_management_factory.create_strategy(mm_type, account_mm_settings)

            if not strategy or not money_management:
                logger.error(f"Failed to create strategy or money management for account {account['account_id']}")
                return

            logger.info(f"Processing account {account['account_id']} with individual settings: {account_mm_settings}")
            
            # Process each symbol/timeframe combination for this account
            # CRITICAL FIX: Use exact symbols from account config without normalization
            for symbol_config in account['symbols']:
                account_symbol = symbol_config['symbol']  # Use exact symbol from account config
                timeframe = symbol_config['timeframe']

                logger.info(f"✅ SYMBOL_DIRECT: Using account-defined symbol {account_symbol} for account {account['account_id']} (no normalization)")

                await self._generate_signal_for_individual_account(
                    account, account_symbol, timeframe, strategy, money_management, qwen_client
                )
            
        except Exception as e:
            logger.error(f"Error processing individual account: {e}")

    async def _generate_signal_for_individual_account(
        self,
        account: Dict[str, Any],
        symbol: str,
        timeframe: str,
        strategy,
        money_management,
        qwen_client: QwenClient
    ):
        """Generate signal for individual account with its specific settings"""
        try:
            # Check if we should generate signal (prevent overtrading)
            signal_key = f"{symbol}_{timeframe}_{strategy.strategy_type.value}_{money_management.strategy_type.value}_{account['account_id']}"

            trading_logger.log_system_event(
                "SIGNAL_GENERATION_START",
                f"Starting signal generation for {signal_key}"
            )

            if not self._should_generate_signal(signal_key):
                trading_logger.log_system_event(
                    "SIGNAL_GENERATION_SKIP",
                    f"Skipping {signal_key} - signal interval not met"
                )
                return

            # Login to account to get market data
            from account_management.models import TradingAccount
            account_obj = TradingAccount(
                account_id=account['account_id'],
                account_number=account['account_number'],
                server=account['server'],
                username=account.get('username', ''),
                password=account['password'],
                strategy_type=account.get('strategy_type', ''),
                money_management_type=account.get('money_management_type', ''),
                symbols=account.get('symbols', []),
                timeframes=account.get('timeframes', []),
                money_management_config=account.get('money_management_config', {})
            )

            if not self.mt5_client.login(account_obj):
                logger.error(f"Failed to login to account {account['account_id']}")
                return

            # CRITICAL FIX: Use account symbol directly (user-defined broker symbols)
            # The symbol parameter already contains the correct broker symbol from account config
            server = account['server']
            broker_symbol = symbol  # Use symbol directly - it's already in correct broker format

            logger.info(f"✅ MARKET_DATA_DIRECT: Getting market data for account {account['account_id']}: {broker_symbol} on {server} (using account-defined symbol)")

            start_time = time.time()
            market_data_dict = self.mt5_client.get_market_data(broker_symbol, timeframe, 200)
            data_retrieval_time = time.time() - start_time

            if not market_data_dict:
                logger.error(f"Failed to get market data for {broker_symbol} (standard: {symbol}) on {server} for account {account['account_id']}")
                trading_logger.log_market_data_retrieval(
                    account['account_id'],
                    broker_symbol,
                    timeframe,
                    0,
                    success=False,
                    error=f"Failed to retrieve market data for {broker_symbol}",
                    processing_time=data_retrieval_time
                )
                return

            trading_logger.log_market_data_retrieval(
                account['account_id'],
                broker_symbol,
                timeframe,
                len(market_data_dict.get('candles', [])),
                success=True,
                processing_time=data_retrieval_time
            )

            # Convert to MarketData object - use standard symbol for consistency
            market_data = MarketData(
                symbol=symbol,  # Use standard symbol, not broker-specific
                timeframe=market_data_dict['timeframe'],
                candles=market_data_dict['candles'],
                current_price=market_data_dict['current_price'],
                spread=market_data_dict['spread'],
                volume=market_data_dict['volume'],
                volatility=market_data_dict['volatility'],
                pip_size=market_data_dict.get('pip_size'),
                pip_value=market_data_dict.get('pip_value'),
                min_volume=market_data_dict.get('min_volume'),
                max_volume=market_data_dict.get('max_volume')
            )

            # Get trade history for this specific account
            trade_history = self.mt5_client.get_trade_history(30)

            # Filter trade history by strategy magic number
            strategy_trades = [
                trade for trade in trade_history
                if trade.get('magic_number') == strategy.magic_number
            ]

            # Get account info
            account_balance = self.mt5_client.get_account_info()
            if not account_balance:
                logger.error(f"Failed to get account info for {account['account_id']}")
                return

            account_info = AccountInfo(
                balance=account_balance.balance,
                equity=account_balance.equity,
                margin=account_balance.margin,
                free_margin=account_balance.free_margin,
                margin_level=account_balance.margin_level,
                currency=account_balance.currency,
                leverage=account_balance.leverage
            )

            # Generate AI prompt with account-specific settings
            prompt = self.prompt_builder.build_trading_prompt(
                strategy=strategy,
                money_management=money_management,
                market_data=market_data,
                trade_history=strategy_trades,
                account_info=account_info
            )

            # Get AI decision
            start_time = time.time()
            ai_response = await qwen_client.generate_trading_decision(prompt)
            ai_processing_time = time.time() - start_time

            trading_logger.log_ai_decision(
                account['account_id'],
                symbol,
                strategy.strategy_type.value,
                len(prompt),
                ai_response,
                ai_processing_time
            )

            # Process the signal for this specific account
            if ai_response.get('action') != 'HOLD':
                logger.info(f"🚀 SIGNAL_PROCESSING: Processing {ai_response.get('action')} signal for {account['account_id']} with confidence {ai_response.get('confidence', 0):.2f}")
                await self._process_signal_for_account(
                    ai_response, symbol, timeframe, strategy, money_management, account, qwen_client
                )
            else:
                logger.info(f"⏸️ SIGNAL_HOLD: AI decided to HOLD for {account['account_id']} - confidence: {ai_response.get('confidence', 0):.2f}")
                logger.info(f"⏸️ HOLD_REASON: {ai_response.get('reasoning', 'No reasoning provided')[:200]}...")

                # Log detailed HOLD analysis
                trading_logger.log_system_event(
                    "SIGNAL_HOLD_DECISION",
                    f"Account {account['account_id']} - {symbol} - HOLD decision with confidence {ai_response.get('confidence', 0):.2f}",
                    "INFO"
                )

            # Update last signal time
            self.last_signal_time[signal_key] = datetime.now()

        except Exception as e:
            logger.error(f"Error generating signal for account {account['account_id']}: {e}")

    async def _process_signal_for_account(
        self,
        signal: Dict[str, Any],
        symbol: str,
        timeframe: str,
        strategy,
        money_management,
        account: Dict[str, Any],
        qwen_client: QwenClient
    ):
        """Process trading signal for specific account with its individual settings"""
        try:
            # Debug: Log the AI signal content
            logger.info(f"AI Signal for {account['account_id']}: {signal}")

            # Validate signal using strategy-specific validation
            from strategies.base_strategy import TradingSignal

            # Handle take_profit_levels if take_profit is None
            take_profit = signal.get('take_profit')
            if not take_profit and signal.get('take_profit_levels'):
                # Use the first take profit level as the primary take profit
                take_profit_levels = signal.get('take_profit_levels') or []  # Handle None case
                if take_profit_levels and len(take_profit_levels) > 0:
                    take_profit = take_profit_levels[0].get('price')
                    logger.info(f"📊 SIGNAL_PROCESSING: Using first take_profit_level as take_profit: {take_profit}")

            trading_signal = TradingSignal(
                action=signal.get('action', 'HOLD'),
                confidence=signal.get('confidence', 0.0),
                entry_price=signal.get('entry_price'),
                stop_loss=signal.get('stop_loss'),
                take_profit=take_profit,
                reasoning=signal.get('reasoning', ''),
                risk_level=signal.get('risk_level', 'MEDIUM')
            )

            logger.info(f"🔍 SIGNAL_VALIDATION: Validating {trading_signal.action} signal for {account['account_id']}")
            logger.info(f"🔍 SIGNAL_DETAILS: Entry: {trading_signal.entry_price}, SL: {trading_signal.stop_loss}, TP: {trading_signal.take_profit}")
            logger.info(f"🔍 SIGNAL_CONFIDENCE: {trading_signal.confidence:.2f} (required: varies by strategy)")

            # Debug: Log what we're validating
            logger.info(f"Validating signal for {account['account_id']}: action={trading_signal.action}, "
                       f"confidence={trading_signal.confidence}, entry={trading_signal.entry_price}, "
                       f"sl={trading_signal.stop_loss}, tp={trading_signal.take_profit}")

            # Create market data for validation with all required fields
            market_data = MarketData(
                symbol=symbol,
                timeframe=timeframe,
                candles=[],
                current_price=signal.get('entry_price', 0.0),
                spread=1.5,  # Default spread
                volume=1000,
                volatility=0.0015,
                pip_size=0.0001,  # Standard pip size for major pairs
                pip_value=10.0,   # Standard pip value
                min_volume=0.01,
                max_volume=100.0
            )

            # Validate signal with strategy
            if not strategy.validate_signal(trading_signal, market_data):
                logger.warning(f"❌ SIGNAL_VALIDATION_FAILED: Signal rejected for account {account['account_id']}")
                logger.warning(f"❌ REJECTION_DETAILS: Strategy: {strategy.__class__.__name__}")
                logger.warning(f"❌ REJECTION_DETAILS: Action: {trading_signal.action}, Confidence: {trading_signal.confidence:.2f}")
                logger.warning(f"❌ REJECTION_DETAILS: Entry: {trading_signal.entry_price}, SL: {trading_signal.stop_loss}, TP: {trading_signal.take_profit}")
                logger.warning(f"❌ REJECTION_DETAILS: Market spread: {market_data.spread}")

                # Log detailed rejection reason
                trading_logger.log_system_event(
                    "SIGNAL_VALIDATION_FAILED",
                    f"Account {account['account_id']} - {symbol} - Signal rejected by {strategy.__class__.__name__} validation",
                    "WARNING"
                )
                return
            else:
                logger.info(f"✅ SIGNAL_VALIDATION_PASSED: Signal approved for account {account['account_id']}")

            # Calculate position size using account-specific money management settings
            account_info = AccountInfo(
                balance=10000.0,  # This should come from actual account info
                equity=10000.0,
                margin=1000.0,
                free_margin=9000.0,
                margin_level=1000.0,
                currency="USD",
                leverage=100
            )

            # Get actual market data for position sizing (no hardcoded values)
            market_data_dict = self.mt5_client.get_market_data(symbol, "M15", 50)
            if not market_data_dict:
                logger.error(f"❌ Failed to get market data for {symbol}, cannot calculate position size")
                return

            # Ensure required fields are present with safe defaults only if missing
            if 'pip_value' not in market_data_dict:
                market_data_dict['pip_value'] = 10.0 if 'JPY' not in symbol else 6.77
                logger.warning(f"⚠️ Using default pip_value for {symbol}: {market_data_dict['pip_value']}")
            if 'pip_size' not in market_data_dict:
                market_data_dict['pip_size'] = 0.001 if 'JPY' in symbol else 0.0001
                logger.warning(f"⚠️ Using default pip_size for {symbol}: {market_data_dict['pip_size']}")
            if 'min_volume' not in market_data_dict:
                market_data_dict['min_volume'] = 0.01
                logger.warning(f"⚠️ Using default min_volume for {symbol}: {market_data_dict['min_volume']}")
            if 'max_volume' not in market_data_dict:
                market_data_dict['max_volume'] = 100.0
                logger.warning(f"⚠️ Using default max_volume for {symbol}: {market_data_dict['max_volume']}")

            # Use account-specific money management settings
            trade_params = money_management.calculate_position_size(
                account_info=account_info,
                symbol=symbol,
                entry_price=trading_signal.entry_price or 0.0,
                stop_loss=trading_signal.stop_loss,
                trade_history=[],
                market_data=market_data_dict
            )

            logger.info(f"Generated signal for account {account['account_id']}: {signal['action']} {symbol} "
                       f"volume={trade_params.volume} with account-specific settings")

            # Execute the actual trade
            await self._execute_trade_signal(
                account=account,
                symbol=symbol,
                signal=signal,
                trade_params=trade_params,
                strategy=strategy,
                money_management=money_management
            )

            trading_logger.log_system_event(
                "SIGNAL_GENERATED",
                f"Account {account['account_id']}: {signal['action']} {symbol} "
                f"volume={trade_params.volume} confidence={signal['confidence']}"
            )

        except Exception as e:
            logger.error(f"Error processing signal for account {account['account_id']}: {e}")

    async def _execute_trade_signal(
        self,
        account: Dict[str, Any],
        symbol: str,
        signal: Dict[str, Any],
        trade_params,
        strategy,
        money_management
    ):
        """Execute the actual trade based on the validated signal"""
        try:
            # RISK ASSESSMENT: Check if trading should be allowed
            account_info = self.mt5_client.get_account_info()
            if account_info:
                from money_management.base_strategy import AccountInfo
                account_info_obj = AccountInfo(
                    balance=account_info.balance,
                    equity=account_info.equity,
                    margin=account_info.margin,
                    free_margin=account_info.margin_free,
                    margin_level=account_info.margin_level,
                    currency=account_info.currency,
                    leverage=account_info.leverage
                )

                # Get recent trade history for risk assessment
                recent_trades = self.mt5_client.get_trade_history(7)  # Last 7 days

                # Perform risk assessment
                risk_assessment = loss_prevention_system.assess_trading_risk(
                    account['account_id'],
                    account_info_obj,
                    recent_trades,
                    account
                )

                # Check if signal should be skipped
                signal_confidence = signal.get('confidence', 0.5)
                if loss_prevention_system.should_skip_signal(
                    account['account_id'],
                    signal_confidence,
                    risk_assessment
                ):
                    logger.warning(f"🚫 TRADE SKIPPED: {account['account_id']} - Risk assessment failed")
                    trading_logger.log_system_event(
                        "TRADE_SKIPPED_RISK",
                        f"Account {account['account_id']}: Trade skipped due to risk assessment - {risk_assessment.risk_level} risk",
                        "WARNING"
                    )
                    return

                # Apply volume adjustment based on risk
                if risk_assessment.recommended_volume_multiplier < 1.0:
                    original_volume = trade_params.volume
                    trade_params.volume *= risk_assessment.recommended_volume_multiplier
                    logger.info(f"📉 VOLUME ADJUSTED: {account['account_id']} - "
                               f"Volume reduced from {original_volume:.3f} to {trade_params.volume:.3f} "
                               f"(multiplier: {risk_assessment.recommended_volume_multiplier:.2f})")

            # CRITICAL FIX: Use account symbol directly (user-defined broker symbols)
            # The symbol parameter already contains the correct broker symbol from account config
            broker_symbol = symbol  # Use symbol directly - it's already in correct broker format
            server = account.get('server', '')

            # Prepare trade request
            action = signal.get('action')
            entry_price = signal.get('entry_price')
            stop_loss = signal.get('stop_loss')
            take_profit = signal.get('take_profit')

            # Use first take_profit_level if take_profit is None
            if not take_profit and signal.get('take_profit_levels'):
                take_profit_levels = signal.get('take_profit_levels') or []  # Handle None case
                if take_profit_levels and len(take_profit_levels) > 0:
                    take_profit = take_profit_levels[0].get('price')

            # Convert action to MT5 order type
            if action == 'BUY':
                order_type = 'BUY'
            elif action == 'SELL':
                order_type = 'SELL'
            else:
                logger.warning(f"Unknown action {action} for account {account['account_id']}")
                return

            # Execute the trade
            logger.info(f"🚀 EXECUTING TRADE: {account['account_id']} {order_type} {broker_symbol} "
                       f"volume={trade_params.volume} entry={entry_price} sl={stop_loss} tp={take_profit}")

            # Place the order using MT5 client
            result = self.mt5_client.place_order(
                symbol=broker_symbol,
                action=order_type,
                volume=trade_params.volume,
                price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"{strategy.strategy_type.value}_{money_management.strategy_type.value}",
                magic_number=strategy.magic_number
            )

            if result is not None:
                logger.info(f"✅ TRADE EXECUTED: {account['account_id']} {order_type} {broker_symbol} "
                           f"order_id={result} volume={trade_params.volume}")

                # CRITICAL FIX: Update daily trade counter after successful execution
                take_profit_levels = signal.get('take_profit_levels') or []  # Handle None case
                is_multiple_tp = len(take_profit_levels) > 1
                self._update_daily_stats(account['account_id'], pnl=0.0, is_multiple_tp=is_multiple_tp)

                trading_logger.log_system_event(
                    "TRADE_EXECUTED",
                    f"Account {account['account_id']}: {order_type} {broker_symbol} "
                    f"order_id={result} volume={trade_params.volume} "
                    f"entry={entry_price} sl={stop_loss} tp={take_profit}"
                )
            else:
                logger.error(f"❌ TRADE EXECUTION FAILED: {account['account_id']} {order_type} {broker_symbol} "
                           f"- Check MT5 logs for details")

                trading_logger.log_system_event(
                    "TRADE_EXECUTION_FAILED",
                    f"Account {account['account_id']}: {order_type} {broker_symbol} "
                    f"volume={trade_params.volume} - Check MT5 logs for details"
                )

        except Exception as e:
            logger.error(f"Error executing trade for account {account['account_id']}: {e}")

    async def _generate_signal_for_symbol(
        self,
        symbol: str,
        timeframe: str,
        strategy,
        money_management,
        accounts: List,
        qwen_client: QwenClient
    ):
        """Generate signal for specific symbol/timeframe"""
        try:
            # Check if we should generate signal (prevent overtrading)
            signal_key = f"{symbol}_{timeframe}_{strategy.strategy_type.value}_{money_management.strategy_type.value}"

            trading_logger.log_system_event(
                "SIGNAL_GENERATION_START",
                f"Starting signal generation for {signal_key} with {len(accounts)} accounts"
            )

            if not self._should_generate_signal(signal_key):
                trading_logger.log_system_event(
                    "SIGNAL_GENERATION_SKIP",
                    f"Skipping {signal_key} - signal interval not met"
                )
                return

            # Login to first account to get market data
            first_account = accounts[0]
            # Convert dict to TradingAccount object for MT5 client
            from account_management.models import TradingAccount
            account_obj = TradingAccount(
                account_id=first_account['account_id'],
                account_number=first_account['account_number'],
                server=first_account['server'],
                username=first_account.get('username', ''),
                password=first_account['password'],
                strategy_type=first_account.get('strategy_type', ''),
                money_management_type=first_account.get('money_management_type', ''),
                symbols=first_account.get('symbols', []),
                timeframes=first_account.get('timeframes', []),
                money_management_config=first_account.get('money_management_config', {})
            )
            if not self.mt5_client.login(account_obj):
                logger.error(f"Failed to login to account {first_account['account_id']}")
                return
            
            # CRITICAL FIX: Use account symbol directly (user-defined broker symbols)
            # The symbol parameter already contains the correct broker symbol from account config
            server = first_account['server']
            broker_symbol = symbol  # Use symbol directly - it's already in correct broker format

            logger.info(f"✅ MARKET_DATA_GROUPED: Getting market data for symbol: {broker_symbol} on {server} (using account-defined symbol)")

            start_time = time.time()
            market_data_dict = self.mt5_client.get_market_data(broker_symbol, timeframe, 200)
            data_retrieval_time = time.time() - start_time

            if not market_data_dict:
                logger.error(f"Failed to get market data for {broker_symbol} (standard: {symbol}) on {server}")
                trading_logger.log_market_data_retrieval(
                    first_account['account_id'],
                    broker_symbol,
                    timeframe,
                    0,
                    success=False,
                    error=f"Failed to retrieve market data for {broker_symbol}",
                    processing_time=data_retrieval_time
                )
                return

            trading_logger.log_market_data_retrieval(
                first_account['account_id'],
                broker_symbol,
                timeframe,
                len(market_data_dict.get('candles', [])),
                success=True,
                processing_time=data_retrieval_time
            )
            
            # Convert to MarketData object - use standard symbol for consistency
            market_data = MarketData(
                symbol=symbol,  # Use standard symbol, not broker-specific
                timeframe=market_data_dict['timeframe'],
                candles=market_data_dict['candles'],
                current_price=market_data_dict['current_price'],
                spread=market_data_dict['spread'],
                volume=market_data_dict['volume'],
                volatility=market_data_dict['volatility'],
                pip_size=market_data_dict.get('pip_size'),
                pip_value=market_data_dict.get('pip_value'),
                min_volume=market_data_dict.get('min_volume'),
                max_volume=market_data_dict.get('max_volume')
            )
            
            # Get trade history
            trade_history = self.mt5_client.get_trade_history(30)
            
            # Filter trade history by strategy magic number
            strategy_trades = [
                trade for trade in trade_history 
                if trade.get('magic_number') == strategy.magic_number
            ]
            
            # Get account info
            account_balance = self.mt5_client.get_account_info()
            if not account_balance:
                logger.error(f"Failed to get account info")
                return
            
            account_info = AccountInfo(
                balance=account_balance.balance,
                equity=account_balance.equity,
                margin=account_balance.margin,
                free_margin=account_balance.free_margin,
                margin_level=account_balance.margin_level,
                currency=account_balance.currency,
                leverage=account_balance.leverage
            )
            
            # Build AI prompt
            prompt = self.prompt_builder.build_trading_prompt(
                strategy=strategy,
                money_management=money_management,
                market_data=market_data,
                trade_history=strategy_trades,
                account_info=account_info,
                additional_context={
                    'accounts_count': len(accounts),
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'market_volatility': market_data.volatility,
                    'account_settings': first_account  # Pass full account settings to AI
                }
            )
            
            # Get AI decision
            start_time = time.time()
            ai_response = await qwen_client.generate_trading_decision(prompt)
            processing_time = time.time() - start_time
            
            # Log AI decision
            trading_logger.log_ai_decision(
                first_account['account_id'],
                symbol,
                strategy.strategy_type.value,
                len(prompt),
                ai_response,
                processing_time
            )
            
            # Process signal for all accounts in group
            if ai_response.get('action') in ['BUY', 'SELL']:
                await self._distribute_signal_to_accounts(
                    ai_response, symbol, timeframe, strategy, money_management, accounts
                )
            
            # Update last signal time
            self.last_signal_time[signal_key] = datetime.now()
            
        except Exception as e:
            logger.error(f"Error generating signal for {symbol}: {e}")
            trading_logger.log_ai_error(
                accounts[0]['account_id'] if accounts else "unknown",
                symbol,
                str(e)
            )
    
    async def _distribute_signal_to_accounts(
        self,
        signal: Dict[str, Any],
        symbol: str,
        timeframe: str,
        strategy,
        money_management,
        accounts: List
    ):
        """Distribute trading signal to all accounts in group"""
        try:
            for account in accounts:
                # Check if this symbol is configured for this account
                account_symbols = [s['symbol'] for s in account['symbols']]
                if symbol not in account_symbols:
                    continue
                
                # Log signal for this account
                trading_logger.log_trade_signal(
                    account['account_id'],
                    symbol,
                    signal,
                    strategy.strategy_type.value,
                    money_management.strategy_type.value
                )
                
                # Store signal for execution (will be processed by trade manager)
                await self._store_signal_for_execution(account, symbol, signal, strategy, money_management)
            
        except Exception as e:
            logger.error(f"Error distributing signal: {e}")
    
    async def _store_signal_for_execution(
        self,
        account,
        symbol: str,
        signal: Dict[str, Any],
        strategy,
        money_management
    ):
        """Execute signal immediately"""
        try:
            # Execute signal immediately instead of storing
            await self._execute_signal(account, symbol, signal, strategy, money_management)

        except Exception as e:
            logger.error(f"Error executing signal: {e}")

    def _validate_signal(self, signal: Dict[str, Any]) -> bool:
        """Validate signal structure and content"""
        try:
            # Check required fields
            required_fields = ['action', 'confidence']
            for field in required_fields:
                if field not in signal:
                    logger.warning(f"Missing required field in signal: {field}")
                    return False

            # Validate action
            valid_actions = ['BUY', 'SELL', 'HOLD', 'CLOSE']
            if signal['action'] not in valid_actions:
                logger.warning(f"Invalid action in signal: {signal['action']}")
                return False

            # Validate confidence
            confidence = signal.get('confidence', 0)
            if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
                logger.warning(f"Invalid confidence in signal: {confidence}")
                return False

            # Validate prices if present
            for price_field in ['entry_price', 'stop_loss', 'take_profit']:
                if price_field in signal:
                    price = signal[price_field]
                    if price is not None and (not isinstance(price, (int, float)) or price <= 0):
                        logger.warning(f"Invalid {price_field} in signal: {price}")
                        return False

            return True

        except Exception as e:
            logger.error(f"Error validating signal: {e}")
            return False

    async def _execute_signal(
        self,
        account,
        symbol: str,
        signal: Dict[str, Any],
        strategy,
        money_management
    ):
        """Execute trading signal immediately"""
        try:
            # Validate signal structure
            if not self._validate_signal(signal):
                logger.warning(f"Invalid signal structure for {account['account_id']}")
                trading_logger.log_signal_validation(
                    account['account_id'], symbol, signal, False, ["Invalid signal structure"]
                )
                return

            action = signal.get('action')
            trading_logger.log_system_event(
                "SIGNAL_EXECUTION_START",
                f"Starting signal execution for {account['account_id']} - {action} {symbol}"
            )

            if action not in ['BUY', 'SELL']:
                logger.debug(f"No action required for signal: {action}")
                trading_logger.log_system_event(
                    "SIGNAL_EXECUTION_SKIP",
                    f"No action required for {account['account_id']} - signal action: {action}"
                )
                return

            # Check if signal has multiple take profit levels for risk assessment
            is_multiple_tp = signal.get('take_profit_levels') and isinstance(signal.get('take_profit_levels'), list)

            # CRITICAL: Validate stop loss is present for percent risk money management
            risk_settings = self._get_account_risk_settings(account)
            if risk_settings.get('require_stop_loss', True) and not signal.get('stop_loss'):
                logger.error(f"❌ STOP LOSS REQUIRED: Signal rejected for {account['account_id']} - no stop loss provided")
                trading_logger.log_system_event(
                    "SIGNAL_EXECUTION_BLOCKED",
                    f"Stop loss required but not provided for {account['account_id']} - {action} {symbol}",
                    "ERROR"
                )
                return

            # Check risk management limits before executing with multiple TP awareness
            if not self._check_risk_limits(account, is_multiple_tp):
                logger.warning(f"Risk limits exceeded for account {account['account_id']}, skipping signal")
                trading_logger.log_system_event(
                    "SIGNAL_EXECUTION_BLOCKED",
                    f"Risk limits exceeded for {account['account_id']} - {action} {symbol}",
                    "WARNING"
                )
                return

            # Convert dict to TradingAccount object for session management
            from account_management.models import TradingAccount
            account_obj = TradingAccount(
                account_id=account['account_id'],
                account_number=account['account_number'],
                server=account['server'],
                username=account.get('username', ''),
                password=account['password'],
                strategy_type=account.get('strategy_type', ''),
                money_management_type=account.get('money_management_type', ''),
                symbols=account.get('symbols', []),
                timeframes=account.get('timeframes', []),
                money_management_config=account.get('money_management_config', {})
            )

            # Use session manager for account operations
            async with session_manager.account_session(account_obj, self.mt5_client) as mt5_client:
                logger.info(f"🔒 Using managed session for account {account['account_id']}")

                # Get account balance for position sizing
                account_balance = mt5_client.get_account_info()
                if not account_balance:
                    logger.error(f"Failed to get account balance for {account['account_id']}")
                    return


                # Calculate position size using money management
                account_info = AccountInfo(
                    balance=account_balance.balance,
                    equity=account_balance.equity,
                    margin=account_balance.margin,
                    free_margin=account_balance.free_margin,
                    margin_level=account_balance.margin_level,
                    currency=account_balance.currency,
                    leverage=account_balance.leverage
                )

                # Get symbol configuration
                symbol_config = None
                for config in account['symbols']:
                    if config['symbol'] == symbol:
                        symbol_config = config
                        break

                if not symbol_config:
                    logger.error(f"Symbol {symbol} not configured for account {account['account_id']}")
                    return

                # Calculate position size
                trade_params = money_management.calculate_position_size(
                    account_info=account_info,
                    symbol=symbol,
                    entry_price=signal.get('entry_price', 0),
                    stop_loss=signal.get('stop_loss', 0),
                    trade_history=[],  # TODO: Get actual trade history
                    market_data={}  # TODO: Get actual market data
                )

                if not trade_params or trade_params.volume <= 0:
                    if trade_params and trade_params.volume == 0:
                        logger.warning(f"Trade skipped for {account['account_id']} - calculated volume would exceed risk limits")
                        logger.warning(f"  Intended risk: ${trade_params.risk_amount:.2f}, Confidence: {trade_params.confidence_level}")
                        trading_logger.log_system_event(
                            "TRADE_SKIPPED_RISK_EXCEEDED",
                            f"Account {account['account_id']} - Risk too high for minimum volume",
                            "WARNING"
                        )
                    else:
                        logger.warning(f"Invalid position size calculated for {account['account_id']}")
                    return

                # Additional risk validation for valid trades
                if not self._check_trade_risk_limits(account, trade_params, account_info):
                    logger.warning(f"Trade skipped for {account['account_id']} - individual trade risk limits exceeded")
                    trading_logger.log_system_event(
                        "TRADE_SKIPPED_RISK_LIMITS",
                        f"Account {account['account_id']} - Individual trade risk too high: ${trade_params.risk_amount:.2f}",
                        "WARNING"
                    )
                    return


                # Check if signal has multiple take profit levels
                has_multiple_tp = signal.get('take_profit_levels') and isinstance(signal.get('take_profit_levels'), list)

                # Enhanced multiple TP eligibility check
                account_info = mt5_client.get_account_info()

                # Check multiple criteria for multiple TP eligibility
                balance_threshold = 100.0  # Minimum balance for multiple TP
                min_volume_per_tp = 0.01  # Minimum volume per TP level
                tp_levels = signal.get('take_profit_levels') or []  # Handle None case

                # Calculate if account can handle multiple TP without risk amplification
                required_total_volume = len(tp_levels) * min_volume_per_tp if tp_levels else 0
                calculated_volume = trade_params.volume if trade_params else 0

                force_single_tp = (
                    not account_info or
                    account_info.balance < balance_threshold or
                    calculated_volume < required_total_volume
                )

                if force_single_tp and has_multiple_tp:
                    if account_info and account_info.balance < balance_threshold:
                        logger.info(f"🔒 BALANCE_THRESHOLD: Forcing single TP (Balance: ${account_info.balance:.2f} < ${balance_threshold})")
                    elif calculated_volume < required_total_volume:
                        logger.info(f"🔒 VOLUME_THRESHOLD: Forcing single TP (Volume: {calculated_volume} < required {required_total_volume})")

                if has_multiple_tp and not force_single_tp:
                    tp_levels = signal['take_profit_levels']

                    # AI decided on multiple TP and account balance allows it
                    logger.info(f"🎯 MULTI_TP_SIGNAL: {len(tp_levels)} TP levels detected for {symbol} (Balance: ${account_info.balance:.2f})")
                    for i, level in enumerate(tp_levels, 1):
                        logger.info(f"  📊 TP{i}: Price {level.get('price')}, Volume {level.get('volume_percent')}%")
                    success = await self._execute_multiple_tp_signal(
                        mt5_client, signal, symbol, action, trade_params, strategy, money_management, account
                    )
                    if success:
                        logger.info(f"✅ Multiple TP signal executed successfully for account {account['account_id']}")
                        self._update_daily_stats(account['account_id'], is_multiple_tp=True)
                    else:
                        logger.error(f"❌ Failed to execute multiple TP signal for account {account['account_id']}")
                else:
                    # Single take profit execution (existing logic or forced for small accounts)
                    if force_single_tp and has_multiple_tp:
                        logger.info(f"🔒 BALANCE_THRESHOLD: Forcing single TP for small account (Balance: ${account_info.balance:.2f} < ${balance_threshold})")
                        # Convert multiple TP to single TP using the first level
                        if signal.get('take_profit_levels'):
                            signal['take_profit'] = signal['take_profit_levels'][0]['price']
                            # Remove multiple TP levels to force single TP execution
                            signal.pop('take_profit_levels', None)

                    success = await self._execute_single_tp_signal(
                        mt5_client, signal, symbol, action, trade_params, strategy, money_management, account
                    )
                    if success:
                        logger.info(f"✅ Single TP signal executed successfully for account {account['account_id']}")
                        self._update_daily_stats(account['account_id'], is_multiple_tp=False)
                    else:
                        logger.error(f"❌ Failed to execute single TP signal for account {account['account_id']}")

        except Exception as e:
            logger.error(f"Error executing signal for account {account['account_id']}: {e}")

    async def _execute_single_tp_signal(
        self,
        mt5_client,
        signal: Dict[str, Any],
        symbol: str,
        action: str,
        trade_params,
        strategy,
        money_management,
        account: Dict[str, Any]
    ) -> bool:
        """Execute signal with single take profit level"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            logger.info(f"🔍 SINGLE_TP: Attempt {retry_count + 1}/{max_retries} for account {account['account_id']}")

            # Validate the trade
            validation_result = self.trade_validator.validate_signal_execution(
                symbol=symbol,
                action=action,
                volume=trade_params.volume,
                price=signal.get('entry_price'),
                stop_loss=signal.get('stop_loss'),
                take_profit=signal.get('take_profit'),
                magic_number=strategy.magic_number
            )

            if validation_result.result == ValidationResult.INVALID:
                logger.error(f"❌ SINGLE_TP: Trade validation failed for account {account['account_id']}")
                for error in validation_result.errors:
                    logger.error(f"❌ SINGLE_TP: {error.error_type}: {error.message}")
                return False

            # Use corrected parameters if available
            final_params = {
                'symbol': symbol,
                'action': action,
                'volume': trade_params.volume,
                'price': signal.get('entry_price'),
                'stop_loss': signal.get('stop_loss'),
                'take_profit': signal.get('take_profit'),
                'magic_number': strategy.magic_number,
                'comment': f"AI_{strategy.strategy_type.value}_{money_management.strategy_type.value}"
            }

            if validation_result.corrected_params:
                final_params.update(validation_result.corrected_params)
                logger.info(f"🔍 SINGLE_TP: Using corrected parameters: {validation_result.corrected_params}")

            # Place order with validated/corrected parameters
            order_id = mt5_client.place_order(**final_params)

            if order_id:
                logger.info(f"✅ SINGLE_TP: Order placed successfully: {order_id}")
                return True
            else:
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"⚠️ SINGLE_TP: Order placement failed, retrying... ({retry_count}/{max_retries})")
                    await asyncio.sleep(1)
                else:
                    logger.error(f"❌ SINGLE_TP: Failed after {max_retries} attempts")

        return False

    async def _execute_multiple_tp_signal(
        self,
        mt5_client,
        signal: Dict[str, Any],
        symbol: str,
        action: str,
        trade_params,
        strategy,
        money_management,
        account: Dict[str, Any]
    ) -> bool:
        """Execute signal with multiple take profit levels"""
        tp_levels = signal.get('take_profit_levels') or []  # Handle None case

        # Validate TP levels structure
        if not self._validate_tp_levels(tp_levels):
            logger.error(f"❌ MULTI_TP: Invalid TP levels structure for account {account['account_id']}")
            return False

        logger.info(f"🎯 MULTI_TP: Executing {len(tp_levels)} TP levels for account {account['account_id']}")

        successful_orders = []
        total_risk_amount = 0.0

        # Get account info for money management calculations
        account_balance = mt5_client.get_account_info()
        if not account_balance:
            logger.error(f"❌ MULTI_TP: Failed to get account info for {account['account_id']}")
            return False

        from money_management.base_strategy import AccountInfo
        account_info = AccountInfo(
            balance=account_balance.balance,
            equity=account_balance.equity,
            margin=account_balance.margin,
            free_margin=account_balance.free_margin,
            margin_level=account_balance.margin_level,
            currency=account_balance.currency,
            leverage=account_balance.leverage
        )

        # Get market data for calculations
        market_data_dict = mt5_client.get_market_data(symbol, "M15", 50)
        if not market_data_dict:
            logger.error(f"❌ MULTI_TP: Failed to get market data for {symbol}")
            return False

        # CRITICAL FIX: Calculate total position size ONCE, then split it
        # This prevents risk amplification where each TP level gets full position size
        total_trade_params = money_management.calculate_position_size(
            account_info=account_info,
            symbol=symbol,
            entry_price=signal.get('entry_price', 0),
            stop_loss=signal.get('stop_loss', 0),
            trade_history=[],
            market_data=market_data_dict
        )

        total_volume = total_trade_params.volume
        total_risk = total_trade_params.risk_amount

        logger.info(f"🎯 MULTI_TP_RISK_FIX: Total calculated volume: {total_volume}, Total risk: ${total_risk:.2f}")

        # CRITICAL FIX: Check if total volume is too small for multiple TP levels
        min_volume = market_data_dict.get('min_volume', 0.01)
        total_min_volume_needed = min_volume * len(tp_levels)

        if total_volume < total_min_volume_needed:
            logger.warning(f"⚠️ MULTI_TP_VOLUME_CHECK: Total volume {total_volume} too small for {len(tp_levels)} TP levels")
            logger.warning(f"⚠️ MULTI_TP_VOLUME_CHECK: Need minimum {total_min_volume_needed}, have {total_volume}")
            logger.warning(f"⚠️ MULTI_TP_VOLUME_CHECK: Converting to single TP to avoid risk amplification")

            # Convert to single TP using the first level
            single_tp_signal = signal.copy()
            single_tp_signal['take_profit'] = tp_levels[0]['price']
            single_tp_signal.pop('take_profit_levels', None)

            # Execute as single TP
            return await self._execute_single_tp_signal(
                mt5_client, single_tp_signal, symbol, action, trade_params, strategy, money_management, account
            )

        for i, tp_level in enumerate(tp_levels, 1):
            tp_price = tp_level['price']
            volume_percent = tp_level['volume_percent']

            # Apply volume percentage to the TOTAL calculated volume
            tp_volume = round(total_volume * (volume_percent / 100), 2)

            # CRITICAL: Only apply minimum volume if the calculated volume is reasonable
            # If it's too small, we should have converted to single TP above
            if tp_volume < min_volume:
                logger.warning(f"⚠️ MULTI_TP: TP{i} volume {tp_volume} below minimum {min_volume}, using minimum")
                tp_volume = min_volume

            # Calculate actual risk for this TP level based on actual volume used
            # This ensures risk tracking is accurate even with minimum volume adjustments
            pip_difference = abs(signal.get('entry_price', 0) - signal.get('stop_loss', 0)) / market_data_dict.get('pip_size', 0.0001)
            pip_value = market_data_dict.get('pip_value', 10.0)
            tp_risk = pip_difference * pip_value * tp_volume
            risk_percent_actual = (tp_risk / account_info.balance) * 100

            logger.info(f"🎯 MULTI_TP: Level {i}/{len(tp_levels)} - Price: {tp_price}, Volume: {tp_volume} ({volume_percent}%)")
            logger.info(f"  💰 Actual Risk: ${tp_risk:.2f} ({risk_percent_actual:.2f}% of balance)")

            # Add to total risk tracking (use actual risk, not theoretical)
            total_risk_amount += tp_risk

            # Validate this TP level
            validation_result = self.trade_validator.validate_signal_execution(
                symbol=symbol,
                action=action,
                volume=tp_volume,
                price=signal.get('entry_price'),
                stop_loss=signal.get('stop_loss'),
                take_profit=tp_price,
                magic_number=strategy.magic_number
            )

            if validation_result.result == ValidationResult.INVALID:
                logger.error(f"❌ MULTI_TP: Level {i} validation failed")
                continue

            # Prepare order parameters for this TP level
            final_params = {
                'symbol': symbol,
                'action': action,
                'volume': tp_volume,
                'price': signal.get('entry_price'),
                'stop_loss': signal.get('stop_loss'),
                'take_profit': tp_price,
                'magic_number': strategy.magic_number,
                'comment': f"AI_{strategy.strategy_type.value}_TP{i}"
            }

            if validation_result.corrected_params:
                final_params.update(validation_result.corrected_params)
                logger.info(f"🔍 MULTI_TP: Level {i} using corrected parameters")

            # Place order for this TP level
            order_id = mt5_client.place_order(**final_params)

            if order_id:
                successful_orders.append({
                    'order_id': order_id,
                    'tp_level': i,
                    'tp_price': tp_price,
                    'volume': tp_volume,
                    'volume_percent': volume_percent
                })
                logger.info(f"✅ MULTI_TP: Level {i} order placed successfully: {order_id}")
            else:
                logger.error(f"❌ MULTI_TP: Level {i} order failed")

        # Log summary with CORRECTED total risk analysis
        if successful_orders:
            # FIXED: Use the original total risk, not accumulated risk
            actual_total_risk = total_risk  # This is the correct total risk
            total_risk_percent = (actual_total_risk / account_info.balance) * 100
            configured_risk_percent = account.get('money_management_settings', {}).get('risk_percent', 2.0)

            logger.info(f"✅ MULTI_TP: {len(successful_orders)}/{len(tp_levels)} orders placed successfully")
            logger.info(f"💰 ACTUAL TOTAL RISK: ${actual_total_risk:.2f} ({total_risk_percent:.2f}% of balance)")
            logger.info(f"🎯 CONFIGURED RISK: {configured_risk_percent}% (${account_info.balance * (configured_risk_percent/100):.2f})")
            logger.info(f"📊 RISK MULTIPLIER: {total_risk_percent / configured_risk_percent:.1f}x")
            logger.info(f"🔧 RISK VALIDATION: {'✅ WITHIN LIMITS' if total_risk_percent <= configured_risk_percent * 1.1 else '⚠️ EXCEEDS CONFIGURED RISK'}")

            for order in successful_orders:
                logger.info(f"  📊 Order {order['order_id']}: TP{order['tp_level']} @ {order['tp_price']} ({order['volume_percent']}%)")
            return True
        else:
            logger.error(f"❌ MULTI_TP: No orders placed successfully")
            return False

    def _validate_tp_levels(self, tp_levels: List[Dict[str, Any]]) -> bool:
        """Validate multiple TP levels structure"""
        if not tp_levels or not isinstance(tp_levels, list):
            logger.error("❌ TP_VALIDATION: TP levels must be a non-empty list")
            return False

        total_percent = 0
        for i, level in enumerate(tp_levels, 1):
            if not isinstance(level, dict):
                logger.error(f"❌ TP_VALIDATION: Level {i} must be a dictionary")
                return False

            if 'price' not in level or 'volume_percent' not in level:
                logger.error(f"❌ TP_VALIDATION: Level {i} missing required fields (price, volume_percent)")
                return False

            try:
                price = float(level['price'])
                volume_percent = float(level['volume_percent'])
            except (ValueError, TypeError):
                logger.error(f"❌ TP_VALIDATION: Level {i} has invalid numeric values")
                return False

            if volume_percent <= 0 or volume_percent > 100:
                logger.error(f"❌ TP_VALIDATION: Level {i} volume_percent must be between 0 and 100")
                return False

            total_percent += volume_percent

        if abs(total_percent - 100) > 0.01:  # Allow small rounding errors
            logger.error(f"❌ TP_VALIDATION: Total volume percent is {total_percent}%, must equal 100%")
            return False

        logger.info(f"✅ TP_VALIDATION: {len(tp_levels)} TP levels validated successfully")
        return True

    def _get_account_risk_settings(self, account: Dict[str, Any]) -> Dict[str, Any]:
        """Get risk management settings for specific account, with fallbacks to environment defaults"""
        # Try both possible keys for money management settings
        money_mgmt = account.get('money_management_config', account.get('money_management_settings', {}))

        # Get account balance for percentage-based calculations
        account_balance = 100.0  # Default fallback
        if self.mt5_client.current_account:
            try:
                balance_info = self.mt5_client.get_account_info()
                if balance_info:
                    account_balance = balance_info.balance
            except Exception as e:
                logger.warning(f"Could not get account balance for {account['account_id']}: {e}")

        # Calculate daily loss limit based on percentage or absolute value
        max_daily_loss_percent = money_mgmt.get('max_daily_loss_percent', 5.0)
        max_daily_loss_dollars = money_mgmt.get('max_daily_loss', self.default_max_daily_loss)

        # Use percentage-based calculation if available, otherwise use absolute value
        if max_daily_loss_percent:
            calculated_daily_loss = account_balance * (max_daily_loss_percent / 100)
        else:
            calculated_daily_loss = max_daily_loss_dollars

        # Validate that required settings are present (no fallbacks for critical settings)
        required_settings = ['max_daily_trades', 'max_open_positions', 'max_pending_orders']
        missing_settings = []

        for setting in required_settings:
            if setting not in money_mgmt:
                missing_settings.append(setting)

        if missing_settings:
            logger.error(f"❌ CRITICAL: Missing required money management settings for account {account['account_id']}: {missing_settings}")
            logger.error(f"❌ Account config: {money_mgmt}")
            # Don't use fallbacks for critical settings - this forces proper configuration
            raise ValueError(f"Account {account['account_id']} missing required settings: {missing_settings}")

        settings = {
            'max_daily_trades': money_mgmt['max_daily_trades'],  # No fallback - must be configured
            'max_open_positions': money_mgmt['max_open_positions'],  # No fallback - must be configured
            'max_pending_orders': money_mgmt['max_pending_orders'],  # No fallback - must be configured
            'max_daily_loss': calculated_daily_loss,
            'max_daily_loss_percent': max_daily_loss_percent,
            'max_drawdown_percent': money_mgmt.get('max_drawdown_percent', self.default_max_drawdown_percent),
            'risk_percent': money_mgmt.get('risk_percent', 2.0),
            'require_stop_loss': money_mgmt.get('_risk_controls', {}).get('require_stop_loss', True)
        }

        logger.info(f"✅ ACCOUNT_SETTINGS: Account {account['account_id']} settings loaded successfully")
        logger.info(f"  📊 Max daily trades: {settings['max_daily_trades']}")
        logger.info(f"  📊 Max open positions: {settings['max_open_positions']}")
        logger.info(f"  📊 Max pending orders: {settings['max_pending_orders']}")
        logger.info(f"  💰 Account balance: ${account_balance:.2f}, Daily loss limit: ${calculated_daily_loss:.2f}")
        return settings

    def _check_risk_limits(self, account: Dict[str, Any], is_multiple_tp: bool = False) -> bool:
        """Check if account is within risk management limits using account-specific settings"""
        try:
            # Get account-specific risk settings
            risk_settings = self._get_account_risk_settings(account)
            account_id = account['account_id']

            # Check daily trade limit
            today = datetime.now().date()
            daily_key = f"{account_id}_{today}"

            if daily_key not in self.daily_trades:
                self.daily_trades[daily_key] = 0

            current_trades = self.daily_trades[daily_key]
            max_trades = risk_settings['max_daily_trades']

            logger.info(f"🔍 DAILY_TRADES_CHECK: Account {account_id} - Current: {current_trades}/{max_trades} trades today ({today})")

            if current_trades >= max_trades:
                logger.warning(f"❌ DAILY_TRADES_LIMIT: Daily trade limit ({max_trades}) reached for account {account_id} - blocking signal generation")
                return False
            else:
                logger.info(f"✅ DAILY_TRADES_OK: {current_trades}/{max_trades} trades - signal generation allowed")

            # Enhanced position and pending order checking
            if not self.mt5_client.current_account:
                return True  # Can't check without login, allow trade

            position_check = self._check_position_limits(account, risk_settings, is_multiple_tp)
            if not position_check:
                return False

            # Check daily loss limit - get actual daily PnL from MT5
            actual_daily_pnl = self._get_actual_daily_pnl(account_id)

            # CRITICAL FIX: Check if we have manual override in daily_pnl tracking
            manual_pnl = self.daily_pnl.get(daily_key, 0.0)

            # Use the worse of actual PnL or manual tracking (for testing scenarios)
            effective_daily_pnl = min(actual_daily_pnl, manual_pnl)

            # Update our tracking with effective PnL
            self.daily_pnl[daily_key] = effective_daily_pnl

            logger.debug(f"Daily PnL check for {account_id}: Actual: ${actual_daily_pnl:.2f}, Manual: ${manual_pnl:.2f}, Effective: ${effective_daily_pnl:.2f} (limit: ${risk_settings['max_daily_loss']:.2f})")

            if effective_daily_pnl <= -risk_settings['max_daily_loss']:
                logger.warning(f"Daily loss limit (${risk_settings['max_daily_loss']:.2f}) reached for account {account_id}. Current loss: ${abs(effective_daily_pnl):.2f}")
                return False

            # CRITICAL FIX: Check maximum drawdown
            drawdown_check = self._check_maximum_drawdown(account_id, risk_settings)
            if not drawdown_check:
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return True  # Allow trade if check fails

    def _check_maximum_drawdown(self, account_id: str, risk_settings: Dict[str, Any]) -> bool:
        """Check if account is within maximum drawdown limits"""
        try:
            max_drawdown_percent = risk_settings.get('max_drawdown_percent', 15.0)

            # Get account info to calculate current drawdown
            if not self.mt5_client.current_account:
                logger.warning(f"⚠️ Cannot check drawdown for {account_id} - MT5 not connected")
                return True  # Allow trade if we can't check

            account_info = self.mt5_client.get_account_info()
            if not account_info:
                logger.warning(f"⚠️ Cannot get account info for drawdown check - {account_id}")
                return True

            # Calculate current drawdown: (Balance - Equity) / Balance * 100
            # If equity is lower than balance, we have a drawdown
            current_balance = account_info.balance
            current_equity = account_info.equity

            if current_balance <= 0:
                logger.warning(f"⚠️ Invalid balance for drawdown calculation: {current_balance}")
                return True

            # Calculate drawdown percentage
            if current_equity < current_balance:
                current_drawdown_percent = ((current_balance - current_equity) / current_balance) * 100
            else:
                current_drawdown_percent = 0.0  # No drawdown if equity >= balance

            logger.info(f"🔍 DRAWDOWN_CHECK: Account {account_id}")
            logger.info(f"  💰 Balance: ${current_balance:.2f}")
            logger.info(f"  💰 Equity: ${current_equity:.2f}")
            logger.info(f"  📉 Current Drawdown: {current_drawdown_percent:.2f}%")
            logger.info(f"  📉 Max Allowed: {max_drawdown_percent:.2f}%")

            if current_drawdown_percent >= max_drawdown_percent:
                logger.warning(f"❌ DRAWDOWN_LIMIT: Maximum drawdown ({max_drawdown_percent:.2f}%) exceeded for account {account_id}. Current: {current_drawdown_percent:.2f}%")
                return False

            logger.info(f"✅ DRAWDOWN_OK: {current_drawdown_percent:.2f}% < {max_drawdown_percent:.2f}% - trading allowed")
            return True

        except Exception as e:
            logger.error(f"❌ Error checking maximum drawdown for {account_id}: {e}")
            return True  # Allow trade if check fails

    def _get_actual_daily_pnl(self, account_id: str) -> float:
        """Get actual daily PnL from MT5 trade history"""
        try:
            if not self.mt5_client.current_account:
                logger.error(f"❌ CRITICAL: Cannot check daily PnL for {account_id} - MT5 not connected")
                # CRITICAL FIX: Return a large negative value to block trading when we can't verify PnL
                return -999999.0

            # Get today's date
            today = datetime.now().date()

            # Get trade history for today
            trade_history = self.mt5_client.get_trade_history(1)  # Last 1 day
            if not trade_history:
                logger.debug(f"No trade history found for {account_id} today")
                return 0.0

            # Calculate total PnL for today's closed trades
            daily_pnl = 0.0
            for trade in trade_history:
                # Check if trade was closed today
                close_time = trade.get('close_time')
                if close_time:
                    if isinstance(close_time, str):
                        # Parse string date
                        try:
                            trade_date = datetime.strptime(close_time.split()[0], '%Y-%m-%d').date()
                        except:
                            continue
                    else:
                        # Assume it's a datetime object
                        trade_date = close_time.date() if hasattr(close_time, 'date') else today

                    if trade_date == today:
                        profit = trade.get('profit', 0.0)
                        swap = trade.get('swap', 0.0)
                        commission = trade.get('commission', 0.0)
                        daily_pnl += (profit + swap + commission)

            # Also add current open positions' unrealized PnL
            positions = self.mt5_client.get_positions()
            if positions:
                for position in positions:
                    daily_pnl += position.get('profit', 0.0)

            return daily_pnl

        except Exception as e:
            logger.error(f"Error getting actual daily PnL: {e}")
            return 0.0

    def _check_trade_risk_limits(self, account: Dict[str, Any], trade_params, account_info) -> bool:
        """Check if individual trade risk is within limits"""
        try:
            risk_settings = self._get_account_risk_settings(account)
            account_id = account['account_id']

            # Check maximum risk per trade (as percentage of balance)
            max_risk_per_trade = risk_settings.get('max_risk_per_trade', 15.0)  # Default 15%
            max_risk_amount = account_info.balance * (max_risk_per_trade / 100)

            if trade_params.risk_amount > max_risk_amount:
                logger.warning(f"Trade risk ${trade_params.risk_amount:.2f} exceeds max per trade ${max_risk_amount:.2f} for account {account_id}")
                return False

            # Check if risk exceeds configured risk percent significantly
            configured_risk_percent = account.get('money_management_settings', {}).get('risk_percent', 2.0)
            configured_risk_amount = account_info.balance * (configured_risk_percent / 100)

            # Get max risk multiplier from account settings
            max_risk_multiplier = account.get('money_management_settings', {}).get('max_risk_multiplier', 10.0)
            max_allowed_risk = configured_risk_amount * max_risk_multiplier

            if trade_params.risk_amount > max_allowed_risk:
                logger.warning(f"Trade risk ${trade_params.risk_amount:.2f} exceeds {max_risk_multiplier}x configured risk ${configured_risk_amount:.2f} for account {account_id}")
                return False

            # Additional safety check: Never risk more than 20% of balance in a single trade
            absolute_max_risk = account_info.balance * 0.20
            if trade_params.risk_amount > absolute_max_risk:
                logger.warning(f"Trade risk ${trade_params.risk_amount:.2f} exceeds absolute maximum 20% of balance ${absolute_max_risk:.2f} for account {account_id}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking trade risk limits: {e}")
            return True  # Allow trade if check fails

    def _check_position_limits(self, account: Dict[str, Any], risk_settings: Dict[str, Any], is_multiple_tp: bool = False) -> bool:
        """Check position and pending order limits with smart counting using account-specific settings"""
        try:
            account_id = account['account_id']
            max_open_positions = risk_settings['max_open_positions']
            max_pending_orders = risk_settings['max_pending_orders']
            total_allowed = max_open_positions + max_pending_orders

            # Get current positions and pending orders
            positions = self.mt5_client.get_positions() or []
            pending_orders = self.mt5_client.get_pending_orders() or []

            current_open_count = len(positions)
            current_pending_count = len(pending_orders)
            total_current = current_open_count + current_pending_count

            # Estimate new orders that would be created
            if is_multiple_tp:
                estimated_new_orders = 3  # Multiple TP typically creates 3 orders
            else:
                estimated_new_orders = 1  # Single TP creates 1 order

            # Check individual limits first
            if current_open_count >= max_open_positions:
                logger.warning(f"⚠️ Open position limit already reached: {current_open_count}/{max_open_positions}")
                return False

            if current_pending_count + estimated_new_orders > max_pending_orders:
                logger.warning(f"⚠️ Pending order limit would be exceeded: {current_pending_count + estimated_new_orders}/{max_pending_orders}")
                return False

            # Check total combined limit (main requirement from user)
            if total_current + estimated_new_orders > total_allowed:
                logger.warning(f"⚠️ Total position limit would be exceeded: {total_current + estimated_new_orders} > {total_allowed} (current: {current_open_count} open + {current_pending_count} pending + {estimated_new_orders} new)")
                return False

            logger.info(f"🔍 POSITION_CHECK: Account {account_id}")
            logger.info(f"  📊 Current: {current_open_count} open + {current_pending_count} pending = {total_current} total")
            logger.info(f"  📊 Estimated new orders: {estimated_new_orders}")
            logger.info(f"  📊 After new signal: {total_current + estimated_new_orders} total")
            logger.info(f"  ⚙️ Limits: {max_open_positions} open, {max_pending_orders} pending, {total_allowed} total")

            logger.info(f"✅ Position limits check passed for account {account_id}")
            return True

        except Exception as e:
            logger.error(f"❌ Error checking position limits for account {account_id}: {e}")
            return False

    def _group_by_strategy(self, orders_or_positions: List[Dict]) -> Dict[int, List[Dict]]:
        """Group orders/positions by magic number (strategy identifier)"""
        groups = {}
        for item in orders_or_positions:
            magic = item.get('magic', 0)
            if magic not in groups:
                groups[magic] = []
            groups[magic].append(item)
        return groups

    def _update_daily_stats(self, account_id: str, pnl: float = 0.0, is_multiple_tp: bool = False):
        """Update daily trading statistics - multiple TP counts as 1 trade"""
        try:
            today = datetime.now().date()
            daily_key = f"{account_id}_{today}"

            # Update trade count
            if daily_key not in self.daily_trades:
                self.daily_trades[daily_key] = 0

            old_count = self.daily_trades[daily_key]
            self.daily_trades[daily_key] += 1
            new_count = self.daily_trades[daily_key]

            # Update P&L
            if daily_key not in self.daily_pnl:
                self.daily_pnl[daily_key] = 0.0
            self.daily_pnl[daily_key] += pnl

            trade_type = "Multi-TP" if is_multiple_tp else "Single-TP"
            logger.info(f"📊 DAILY_STATS_UPDATE: Account {account_id} ({trade_type}) - Trade count: {old_count} → {new_count}, P&L: ${self.daily_pnl[daily_key]:.2f}")

        except Exception as e:
            logger.error(f"Error updating daily stats: {e}")

    def _should_generate_signal(self, signal_key: str) -> bool:
        """Check if we should generate a new signal (prevent overtrading)"""
        try:
            last_time = self.last_signal_time.get(signal_key)
            if not last_time:
                return True
            
            time_diff = datetime.now() - last_time
            return time_diff.total_seconds() >= (self.min_signal_interval * 60)
            
        except Exception as e:
            logger.error(f"Error checking signal timing: {e}")
            return False
    
    def _is_market_open(self) -> bool:
        """Check if forex market is open"""
        try:
            # Check if weekend trading is enabled for testing
            import os
            enable_weekend_trading = os.getenv('ENABLE_WEEKEND_TRADING', 'false').lower() == 'true'

            if enable_weekend_trading:
                logger.info("Weekend trading enabled - allowing signal generation")
                return True

            now = datetime.now()
            weekday = now.weekday()  # 0=Monday, 6=Sunday
            hour = now.hour

            # Forex market is closed from Friday 22:00 to Sunday 22:00 GMT
            if weekday == 5 and hour >= 22:  # Friday after 22:00
                return False
            elif weekday == 6:  # Saturday (all day)
                return False
            elif weekday == 0 and hour < 22:  # Sunday before 22:00
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking market hours: {e}")
            return False  # Conservative approach
