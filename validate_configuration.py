#!/usr/bin/env python3
"""
Configuration Validation Script
Validates that all accounts have proper configuration for the enhanced trading system
"""

import json
from pathlib import Path

def validate_account_configuration():
    """Validate accounts.json configuration"""
    print("🔍 VALIDATING ACCOUNT CONFIGURATION")
    print("=" * 50)
    
    try:
        with open('config/accounts.json', 'r') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ Failed to load accounts.json: {e}")
        return False
    
    accounts = config.get('accounts', [])
    print(f"📊 Found {len(accounts)} accounts")
    
    validation_passed = True
    
    for i, account in enumerate(accounts):
        account_id = account.get('account_id', f'Account_{i}')
        print(f"\n🔍 Validating: {account_id}")
        
        # Required fields
        required_fields = [
            'account_id', 'account_number', 'password', 'server',
            'strategy', 'money_management', 'symbols', 'money_management_settings'
        ]
        
        for field in required_fields:
            if field not in account:
                print(f"   ❌ Missing required field: {field}")
                validation_passed = False
            else:
                print(f"   ✅ Has: {field}")
        
        # Validate strategy configuration
        strategy = account.get('strategy')
        strategy_selection = account.get('strategy_selection', {})
        
        if strategy_selection.get('mode') == 'dynamic':
            print(f"   🎯 Dynamic strategy selection enabled (fallback: {strategy})")
            
            # Check dynamic strategy configuration
            if 'available_strategies' not in strategy_selection:
                print(f"   ❌ Missing: strategy_selection.available_strategies")
                validation_passed = False
            else:
                available = strategy_selection['available_strategies']
                print(f"   ✅ Available strategies: {available}")
                
                # Check if fallback strategy is in available strategies
                if strategy not in available:
                    print(f"   ⚠️  WARNING: Fallback strategy '{strategy}' not in available strategies")
            
            if 'selection_criteria' not in strategy_selection:
                print(f"   ❌ Missing: strategy_selection.selection_criteria")
                validation_passed = False
            else:
                print(f"   ✅ Has selection criteria")
        else:
            print(f"   📌 Static strategy: {strategy}")
        
        # Validate risk management settings
        mm_settings = account.get('money_management_settings', {})
        required_risk_fields = [
            'max_daily_loss', 'max_drawdown_percent', 'max_open_positions'
        ]
        
        for field in required_risk_fields:
            if field not in mm_settings:
                print(f"   ❌ Missing risk management: {field}")
                validation_passed = False
            else:
                value = mm_settings[field]
                print(f"   ✅ Risk setting: {field} = {value}")
        
        # Check risk controls
        risk_controls = mm_settings.get('_risk_controls', {})
        if 'max_risk_per_trade_percent' in risk_controls:
            print(f"   ✅ Enhanced risk controls configured")
        else:
            print(f"   ⚠️  Missing enhanced risk controls")
    
    print(f"\n🎯 VALIDATION SUMMARY")
    print("=" * 50)
    
    if validation_passed:
        print("✅ ALL ACCOUNTS PROPERLY CONFIGURED")
        print("✅ Dynamic strategy selection ready")
        print("✅ Risk management settings complete")
        print("✅ Fallback strategies properly defined")
        
        print(f"\n📋 CONFIGURATION SUMMARY:")
        dynamic_accounts = [a for a in accounts if a.get('strategy_selection', {}).get('mode') == 'dynamic']
        static_accounts = [a for a in accounts if a.get('strategy_selection', {}).get('mode') != 'dynamic']
        
        print(f"   • {len(dynamic_accounts)} accounts with dynamic strategy selection")
        print(f"   • {len(static_accounts)} accounts with static strategy")
        
        if dynamic_accounts:
            print(f"   • Dynamic accounts: {[a['account_id'] for a in dynamic_accounts]}")
        if static_accounts:
            print(f"   • Static accounts: {[a['account_id'] for a in static_accounts]}")
        
        return True
    else:
        print("❌ CONFIGURATION ISSUES FOUND")
        print("❌ Please fix the issues above before running the trading system")
        return False

def explain_configuration_logic():
    """Explain the relationship between static and dynamic strategy configuration"""
    print(f"\n📚 CONFIGURATION LOGIC EXPLANATION")
    print("=" * 50)
    
    print("🔧 STRATEGY FIELD RELATIONSHIP:")
    print("   • 'strategy' field = Default/Fallback strategy")
    print("   • 'strategy_selection' = Dynamic AI-driven selection")
    print("   • When dynamic mode is enabled:")
    print("     - AI selects optimal strategy based on market conditions")
    print("     - Falls back to 'strategy' field if AI selection fails")
    print("     - Uses 'strategy' field during system initialization")
    print("   • When dynamic mode is disabled:")
    print("     - Always uses the 'strategy' field")
    
    print(f"\n🛡️  RISK MANAGEMENT INTEGRATION:")
    print("   • 'max_daily_loss' = Maximum dollar loss per day")
    print("   • 'max_drawdown_percent' = Maximum account drawdown %")
    print("   • 'max_risk_per_trade_percent' = Risk per trade %")
    print("   • Loss prevention system uses these limits automatically")
    
    print(f"\n🎯 BEST PRACTICES:")
    print("   • Keep 'strategy' field as a conservative fallback")
    print("   • Set 'available_strategies' to tested strategies only")
    print("   • Configure risk limits based on account size")
    print("   • Test with dynamic mode disabled first")

def main():
    """Main validation function"""
    success = validate_account_configuration()
    explain_configuration_logic()
    
    if success:
        print(f"\n🚀 SYSTEM READY FOR DEPLOYMENT")
        print("   • All configurations validated")
        print("   • No manual setup required")
        print("   • Enhanced features fully integrated")
    else:
        print(f"\n⚠️  CONFIGURATION NEEDS ATTENTION")
        print("   • Fix validation errors before deployment")
    
    return success

if __name__ == "__main__":
    main()
