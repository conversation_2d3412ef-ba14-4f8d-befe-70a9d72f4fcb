#!/usr/bin/env python3
"""
Quick verification script for MarketData fix
Tests that the pip_size error has been resolved
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_marketdata_fix():
    """Test that MarketData can be created with pip_size parameter"""
    print("🔧 Testing MarketData pip_size fix...")
    
    try:
        from strategies.base_strategy import MarketData
        print("✅ Successfully imported MarketData")
        
        # Test 1: Create MarketData with pip_size parameter
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="M15",
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015,
            pip_size=0.0001  # This was causing the error before
        )
        print("✅ Successfully created MarketData with pip_size parameter")
        print(f"   Symbol: {market_data.symbol}")
        print(f"   Pip Size: {market_data.pip_size}")
        
        # Test 2: Create MarketData with all new optional parameters
        market_data_full = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[
                {'time': '2025-08-01 10:00:00', 'open': 1.1000, 'high': 1.1020, 'low': 1.0980, 'close': 1.1010, 'volume': 1000}
            ],
            current_price=1.1010,
            spread=1.5,
            volume=1000,
            volatility=0.0015,
            pip_size=0.0001,
            pip_value=10.0,
            min_volume=0.01,
            max_volume=100.0,
            bid=1.1009,
            ask=1.1011,
            timestamp="2025-08-06T10:00:00"
        )
        print("✅ Successfully created MarketData with all optional parameters")
        print(f"   Pip Value: {market_data_full.pip_value}")
        print(f"   Min Volume: {market_data_full.min_volume}")
        print(f"   Max Volume: {market_data_full.max_volume}")
        print(f"   Bid: {market_data_full.bid}")
        print(f"   Ask: {market_data_full.ask}")
        
        # Test 3: Test backward compatibility (without new parameters)
        market_data_old = MarketData(
            symbol="GBPUSD",
            timeframe="M30",
            candles=[],
            current_price=1.2650,
            spread=2.0,
            volume=800,
            volatility=0.0020
        )
        print("✅ Successfully created MarketData without optional parameters (backward compatibility)")
        print(f"   Symbol: {market_data_old.symbol}")
        print(f"   Pip Size (should be None): {market_data_old.pip_size}")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ MarketData pip_size error has been successfully fixed!")
        print("✅ The system can now handle MarketData with pip_size parameter!")
        print("✅ Backward compatibility is maintained!")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        print("❌ MarketData fix verification failed!")
        return False

def test_strategy_validation():
    """Test that strategy validation works with the fixed MarketData"""
    print("\n🔧 Testing strategy validation with fixed MarketData...")
    
    try:
        from strategies.trend_following import TrendFollowingStrategy
        from strategies.base_strategy import TradingSignal
        
        # Create strategy
        strategy = TrendFollowingStrategy({'magic_number': 12345})
        print("✅ Successfully created TrendFollowingStrategy")
        
        # Create MarketData with pip_size (this was failing before)
        from strategies.base_strategy import MarketData
        market_data = MarketData(
            symbol="EURUSD",
            timeframe="H1",
            candles=[],
            current_price=1.1000,
            spread=1.5,
            volume=1000,
            volatility=0.0015,
            pip_size=0.0001  # This parameter was causing issues
        )
        print("✅ Successfully created MarketData for strategy validation")
        
        # Create a valid trading signal
        signal = TradingSignal(
            action="BUY",
            confidence=0.85,
            entry_price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1150,
            reasoning="Test signal for validation",
            risk_level="MEDIUM"
        )
        print("✅ Successfully created TradingSignal")
        
        # Test signal validation (this should work now)
        is_valid = strategy.validate_signal(signal, market_data)
        print(f"✅ Signal validation result: {is_valid}")
        
        print("\n🎉 STRATEGY VALIDATION TEST PASSED!")
        print("✅ Strategy validation works with fixed MarketData!")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        print("❌ Strategy validation test failed!")
        return False

def main():
    """Main test function"""
    print("🚀 MARKETDATA FIX VERIFICATION")
    print("=" * 50)
    
    # Test 1: Basic MarketData fix
    test1_passed = test_marketdata_fix()
    
    # Test 2: Strategy validation with fixed MarketData
    test2_passed = test_strategy_validation()
    
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED!")
        print("✅ MarketData pip_size error has been completely resolved!")
        print("✅ The trading system is ready for use!")
        print("\n🎯 NEXT STEPS:")
        print("   1. Run the full test suite: python run_comprehensive_tests.py")
        print("   2. Deploy to production with demo account ********")
        print("   3. Monitor system performance and logs")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        print("❌ Please review the errors above and fix any remaining issues.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
