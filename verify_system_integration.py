#!/usr/bin/env python3
"""
Comprehensive System Integration Verification
Verify all new implementations are properly integrated and compatible
"""

import sys
import os
from pathlib import Path
import json
import traceback

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

def test_imports():
    """Test all new module imports"""
    print("🔍 TESTING MODULE IMPORTS")
    print("=" * 40)
    
    import_tests = [
        ("strategy_selection.dynamic_strategy_selector", "dynamic_strategy_selector"),
        ("risk_management.loss_prevention", "loss_prevention_system"),
        ("trade_management.trade_manager", "TradeManager"),
        ("signal_generation.signal_generator", "SignalGenerator"),
        ("account_management.account_manager", "AccountManager"),
        ("ai_integration.qwen_client", "QwenClient"),
        ("mt5_integration.mt5_client", "MT5Client"),
    ]
    
    failed_imports = []
    
    for module_name, class_name in import_tests:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}")
        except Exception as e:
            print(f"❌ {module_name}.{class_name}: {e}")
            failed_imports.append((module_name, class_name, str(e)))
    
    return failed_imports

def test_configuration():
    """Test configuration file completeness"""
    print("\n🔍 TESTING CONFIGURATION FILES")
    print("=" * 40)
    
    issues = []
    
    # Test accounts.json
    try:
        with open('config/accounts.json', 'r') as f:
            config = json.load(f)
        
        accounts = config.get('accounts', [])
        print(f"📊 Found {len(accounts)} accounts in configuration")
        
        required_fields = [
            'account_id', 'account_number', 'password', 'server',
            'strategy', 'money_management', 'symbols', 'money_management_settings'
        ]
        
        new_fields = ['strategy_selection']
        risk_fields = ['max_daily_loss', 'max_drawdown_percent']
        
        for i, account in enumerate(accounts):
            account_id = account.get('account_id', f'Account_{i}')
            print(f"\n🔍 Checking account: {account_id}")
            
            # Check required fields
            for field in required_fields:
                if field not in account:
                    issues.append(f"Account {account_id}: Missing required field '{field}'")
                    print(f"   ❌ Missing: {field}")
                else:
                    print(f"   ✅ Has: {field}")
            
            # Check new dynamic strategy selection
            if 'strategy_selection' in account:
                strategy_sel = account['strategy_selection']
                if strategy_sel.get('mode') == 'dynamic':
                    required_strategy_fields = ['available_strategies', 'selection_criteria']
                    for field in required_strategy_fields:
                        if field not in strategy_sel:
                            issues.append(f"Account {account_id}: Missing strategy_selection.{field}")
                            print(f"   ❌ Missing strategy_selection.{field}")
                        else:
                            print(f"   ✅ Has strategy_selection.{field}")
                else:
                    print(f"   ℹ️  Strategy selection mode: {strategy_sel.get('mode', 'static')}")
            else:
                issues.append(f"Account {account_id}: Missing 'strategy_selection' configuration")
                print(f"   ⚠️  Missing: strategy_selection")
            
            # Check risk management fields
            mm_settings = account.get('money_management_settings', {})
            for field in risk_fields:
                if field not in mm_settings:
                    issues.append(f"Account {account_id}: Missing money_management_settings.{field}")
                    print(f"   ❌ Missing mm_settings.{field}")
                else:
                    print(f"   ✅ Has mm_settings.{field}")
    
    except Exception as e:
        issues.append(f"Failed to load accounts.json: {e}")
        print(f"❌ Failed to load accounts.json: {e}")
    
    return issues

def test_class_instantiation():
    """Test that new classes can be instantiated"""
    print("\n🔍 TESTING CLASS INSTANTIATION")
    print("=" * 40)
    
    instantiation_errors = []
    
    try:
        # Test DynamicStrategySelector
        from strategy_selection.dynamic_strategy_selector import DynamicStrategySelector
        selector = DynamicStrategySelector()
        print("✅ DynamicStrategySelector instantiated")
    except Exception as e:
        instantiation_errors.append(f"DynamicStrategySelector: {e}")
        print(f"❌ DynamicStrategySelector: {e}")
    
    try:
        # Test LossPreventionSystem
        from risk_management.loss_prevention import LossPreventionSystem
        loss_prevention = LossPreventionSystem()
        print("✅ LossPreventionSystem instantiated")
    except Exception as e:
        instantiation_errors.append(f"LossPreventionSystem: {e}")
        print(f"❌ LossPreventionSystem: {e}")
    
    try:
        # Test AccountManager (existing)
        from account_management.account_manager import AccountManager
        account_manager = AccountManager()
        print("✅ AccountManager instantiated")
    except Exception as e:
        instantiation_errors.append(f"AccountManager: {e}")
        print(f"❌ AccountManager: {e}")
    
    try:
        # Test TradeManager with AccountManager
        from trade_management.trade_manager import TradeManager
        from account_management.account_manager import AccountManager
        account_manager = AccountManager()
        trade_manager = TradeManager(account_manager)
        print("✅ TradeManager instantiated with AccountManager")
    except Exception as e:
        instantiation_errors.append(f"TradeManager: {e}")
        print(f"❌ TradeManager: {e}")
    
    return instantiation_errors

def test_integration_compatibility():
    """Test that new features integrate with existing system"""
    print("\n🔍 TESTING INTEGRATION COMPATIBILITY")
    print("=" * 40)
    
    compatibility_issues = []
    
    try:
        # Test that SignalGenerator can import new modules
        from signal_generation.signal_generator import SignalGenerator
        from account_management.account_manager import AccountManager
        
        account_manager = AccountManager()
        signal_generator = SignalGenerator(account_manager)
        print("✅ SignalGenerator with new imports works")
        
        # Check if new attributes exist
        if hasattr(signal_generator, 'mt5_client'):
            print("✅ SignalGenerator has mt5_client attribute")
        else:
            compatibility_issues.append("SignalGenerator missing mt5_client attribute")
            
    except Exception as e:
        compatibility_issues.append(f"SignalGenerator integration: {e}")
        print(f"❌ SignalGenerator integration: {e}")
    
    try:
        # Test TradeManager integration
        from trade_management.trade_manager import TradeManager
        from account_management.account_manager import AccountManager
        
        account_manager = AccountManager()
        trade_manager = TradeManager(account_manager)
        
        # Check if new attributes exist
        if hasattr(trade_manager, 'ai_client'):
            print("✅ TradeManager has ai_client attribute")
        else:
            compatibility_issues.append("TradeManager missing ai_client attribute")
            
    except Exception as e:
        compatibility_issues.append(f"TradeManager integration: {e}")
        print(f"❌ TradeManager integration: {e}")
    
    return compatibility_issues

def main():
    """Run comprehensive system verification"""
    print("🔍 COMPREHENSIVE SYSTEM INTEGRATION VERIFICATION")
    print("=" * 60)
    
    all_issues = []
    
    # Test 1: Module imports
    failed_imports = test_imports()
    all_issues.extend([f"IMPORT: {issue[0]}.{issue[1]} - {issue[2]}" for issue in failed_imports])
    
    # Test 2: Configuration completeness
    config_issues = test_configuration()
    all_issues.extend([f"CONFIG: {issue}" for issue in config_issues])
    
    # Test 3: Class instantiation
    instantiation_errors = test_class_instantiation()
    all_issues.extend([f"INSTANTIATION: {issue}" for issue in instantiation_errors])
    
    # Test 4: Integration compatibility
    compatibility_issues = test_integration_compatibility()
    all_issues.extend([f"COMPATIBILITY: {issue}" for issue in compatibility_issues])
    
    # Summary
    print(f"\n🎯 VERIFICATION SUMMARY")
    print("=" * 60)
    
    if not all_issues:
        print("✅ ALL TESTS PASSED - System is fully integrated and ready")
        print("✅ No manual configuration steps required")
        print("✅ All new features are compatible with existing codebase")
        return True
    else:
        print(f"❌ FOUND {len(all_issues)} ISSUES:")
        for issue in all_issues:
            print(f"   • {issue}")
        
        print(f"\n🔧 RECOMMENDED ACTIONS:")
        if any("IMPORT" in issue for issue in all_issues):
            print("   • Fix import statements and module paths")
        if any("CONFIG" in issue for issue in all_issues):
            print("   • Complete configuration file updates")
        if any("INSTANTIATION" in issue for issue in all_issues):
            print("   • Fix class initialization issues")
        if any("COMPATIBILITY" in issue for issue in all_issues):
            print("   • Resolve integration compatibility problems")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
